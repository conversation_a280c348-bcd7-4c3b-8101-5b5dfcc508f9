# GoAssistant

A comprehensive AI-powered development platform that seamlessly integrates conversational AI with specialized development tools. GoAssistant provides dedicated interfaces for database management, container orchestration, cloud services, and more - all enhanced by intelligent AI assistance.

## Overview

GoAssistant transforms the development workflow by combining the power of AI agents with purpose-built tools for modern development tasks. Unlike traditional AI assistants that only offer chat-based interactions, GoAssistant provides full-featured specialized interfaces for each development domain while maintaining AI assistance throughout the platform.

The platform demonstrates advanced Go development practices, including pure standard library usage for the web server, sophisticated error handling, and clean architecture principles. It serves as both a powerful productivity platform and a reference implementation for building complex Go applications.

## Key Features

### 🏠 Unified Platform Experience
GoAssistant provides a cohesive development environment where AI assistance enhances every tool. The platform features a modern web interface with dedicated workspaces for each development domain, ensuring optimal workflows without context switching.

### 🤖 Intelligent AI Integration
The platform leverages Claude and Gemini models through specialized agents that understand the context of your current tool. Whether you're optimizing SQL queries or troubleshooting Kubernetes deployments, the AI provides relevant, actionable assistance based on deep domain knowledge.

### 🛠️ Specialized Development Tools

**SQL Manager** - A complete database development environment featuring visual schema exploration, intelligent query optimization, and migration management. The integrated AI understands your database structure and suggests performance improvements.

**Kubernetes Dashboard** - Comprehensive cluster management with real-time monitoring, resource visualization, and deployment automation. AI assistance helps with troubleshooting, resource optimization, and best practice enforcement.

**Docker Manager** - Full container lifecycle management including image building, registry browsing, and compose orchestration. The AI provides Dockerfile optimization and security recommendations.

**Go Development Suite** - Advanced tools for Go developers including real-time code analysis, performance profiling with pprof, memory analysis, and race detection. AI-powered code review and optimization suggestions.

**Cloudflare Integration** - Manage all Cloudflare services from DNS and tunnels to R2 storage and Workers. AI helps optimize configurations and troubleshoot issues.

### 🗣️ Conversational Interface
Beyond specialized tools, GoAssistant offers a sophisticated chat interface where you can interact with domain-specific AI agents. These agents can invoke tools, analyze results, and provide contextual guidance throughout your development workflow.

### 📁 Workspace Management
Organize projects, share resources with team members, and maintain consistent environments across different tools. The workspace system ensures all your tools have access to the same context and files.

## Use Cases

The platform serves diverse development needs through its specialized tools and AI integration:

**Database Development and Optimization** becomes streamlined with the SQL Manager's visual tools and AI-powered optimization. Development teams can collaboratively design schemas, optimize queries, and manage migrations with confidence, knowing the AI assistant provides real-time guidance based on best practices and performance metrics.

**Container and Orchestration Management** transforms from command-line complexity to visual clarity. The Kubernetes Dashboard and Docker Manager provide intuitive interfaces for managing containerized applications, while AI assistance helps prevent common pitfalls and suggests optimization opportunities.

**Go Application Development** reaches new levels of efficiency with integrated profiling, analysis, and debugging tools. The AI understands Go's idioms and patterns, providing suggestions that align with the language's philosophy while helping identify performance bottlenecks and potential issues.

**Cloud Service Integration** becomes manageable through unified interfaces. Instead of juggling multiple dashboards and CLI tools, developers can manage Cloudflare services, deploy applications, and configure infrastructure from a single platform with consistent patterns.

**Collaborative Development** flourishes through shared workspaces and team features. Teams can work together on database schemas, review Kubernetes configurations, and share debugging sessions, all while the AI provides consistent guidance based on organizational best practices.

**Learning and Skill Development** happens naturally as developers use the platform. The AI doesn't just solve problems but explains its reasoning, helping team members understand why certain approaches work better and gradually building their expertise across different domains.

## Technology Stack

- **Language**: Go 1.24+ (utilizing latest stdlib enhancements)
- **Web Server**: Pure Go `net/http` (no frameworks)
- **Database**: PostgreSQL 15+ with pgvector extension
- **Database Driver**: [pgx](https://github.com/jackc/pgx) for native PostgreSQL access
- **SQL Generation**: [sqlc](https://sqlc.dev) for type-safe queries
- **AI Integration**:
  - Claude (Anthropic)
  - Gemini (Google)
  - [LangChain Go](https://github.com/tmc/langchaingo) for agent framework
- **Container Orchestration**: Kubernetes via Kind for local development
- **Search**: Self-hosted SearXNG for privacy-focused web search
- **UI Stack**:
  - [Templ](https://templ.guide) for type-safe templates
  - [HTMX](https://htmx.org) for dynamic interactions
  - Material Design 3 components ([templui.io](https://templui.io))
- **Logging**: Go 1.21+ `log/slog` for structured logging
- **Error Handling**: Wrapped errors with `fmt.Errorf` and `%w`
- **Observability**: OpenTelemetry, Prometheus, Loki, Jaeger

## Installation

### Prerequisites
- Go 1.24 or higher
- PostgreSQL 15+ with pgvector extension
- Docker and Kind (for Kubernetes features)
- Git

### Quick Start

```bash
# Clone the repository
git clone https://github.com/yourusername/goassistant.git
cd goassistant

# Install pgvector extension for PostgreSQL
# For Ubuntu/Debian:
sudo apt install postgresql-15-pgvector
# For macOS:
brew install pgvector

# Setup development environment
make setup

# This will:
# - Install Go dependencies
# - Install development tools (sqlc, golangci-lint)
# - Setup Kind cluster with required services
# - Initialize database with pgvector

# Configure environment
cp .env.example .env
# Edit .env with your API keys and configuration

# Run database migrations
make migrate

# Generate sqlc code
make generate

# Start the application
make run
```

### Docker Compose Setup (Alternative)

```bash
# Start all services including PostgreSQL with pgvector
docker-compose up -d

# Run migrations
make migrate

# Start the application
make run
```

## Configuration

GoAssistant uses environment-based configuration following the 12-factor app methodology. See `.env.example` for all available options.

### Required Configuration
- `CLAUDE_API_KEY` or `GEMINI_API_KEY` (at least one AI provider)
- `DATABASE_URL` (PostgreSQL connection string with pgvector)

### Optional Services
- `CLOUDFLARE_API_TOKEN` - For Cloudflare integrations
- `KUBERNETES_CONFIG_PATH` - K8s cluster configuration
- `DOCKER_HOST` - Docker daemon connection
- `SEARXNG_URL` - SearXNG instance URL
- Observability endpoints (Prometheus, Loki, Jaeger)

### Development Configuration
```bash
# Copy example configuration
cp .env.example .env

# Edit with your settings
vim .env

# Required: Set at least one AI provider
export CLAUDE_API_KEY="your-claude-key"
# or
export GEMINI_API_KEY="your-gemini-key"

# Required: PostgreSQL with pgvector
export DATABASE_URL="postgres://user:pass@localhost/goassistant?sslmode=disable"
```

## Usage

### CLI Mode
```bash
# Interactive mode
goassistant cli

# Direct command
goassistant ask "Explain Go's memory model"
```

### Web Interface
```bash
# Start web server
goassistant serve

# Access at http://localhost:8080
```

## Platform Architecture

GoAssistant is built as a modular platform where each tool operates as a specialized service while sharing common infrastructure. The architecture emphasizes clean separation between tools while maintaining seamless integration through shared services and AI assistance. This design allows each tool to be optimized for its specific use case while benefiting from platform-wide features like authentication, file management, and AI context.

The platform follows Go's idiomatic patterns throughout, using only the standard library for HTTP handling, demonstrating that complex applications can be built without heavy frameworks. Each tool service maintains its own data models and business logic while adhering to common interface contracts for consistency.

## Development Philosophy

This project strictly adheres to Go's idiomatic patterns and best practices:

- **No Web Frameworks**: Pure `net/http` for complete control and clarity
- **Interface-Driven Design**: Accept interfaces, return structs
- **Error Handling**: Explicit error handling with wrapped errors
- **Standard Library First**: Maximize use of Go's excellent standard library
- **Type Safety**: Leverage Go's type system with sqlc for database queries
- **Composition over Inheritance**: Build complex behavior through composition
- **Simplicity**: Prefer simple, clear code over clever abstractions

## Documentation

- [Architecture Overview](docs/ARCHITECTURE.md)
- [Development Guide](docs/DEVELOPMENT.md)
- [API Reference](docs/API.md)
- [Configuration Guide](docs/CONFIGURATION.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Process
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Follow Go best practices and project conventions
4. Add comprehensive tests (aim for >80% coverage)
5. Ensure all tests pass (`make test`)
6. Run linters (`make lint`)
7. Commit with clear messages (`git commit -m 'Add amazing feature'`)
8. Push to your fork (`git push origin feature/amazing-feature`)
9. Open a Pull Request with detailed description

### Areas We Need Help
- Additional LangChain tool implementations
- Performance optimizations
- Documentation improvements
- Test coverage expansion
- UI/UX enhancements
- Community tool plugins

## Roadmap

See our [project board](https://github.com/yourusername/goassistant/projects) for current progress.

### Phase 1: Foundation (Current)
- Core architecture with pure Go stdlib
- Basic Claude/Gemini integration
- PostgreSQL with pgvector setup
- LangChain tool adapters
- Web UI with Templ + HTMX

### Phase 2: Agent Development
- Development assistant agent with Go AST integration
- Database expert agent with query optimization
- Infrastructure management agent
- Research and analysis agent
- Memory system implementation

### Phase 3: Advanced Features
- Multi-agent collaboration
- Advanced RAG with re-ranking
- Real-time monitoring dashboards
- Plugin system for custom tools
- MCP (Model Context Protocol) integration when Go support is released

### Phase 4: Production Ready
- Performance optimization
- Comprehensive test coverage
- Security hardening
- Documentation completion
- Community plugins support

## System Requirements

### Minimum Requirements
- Go 1.24+
- PostgreSQL 15+ with pgvector extension
- 4GB RAM
- 10GB disk space

### Recommended Setup
- Go 1.24+ (latest version)
- PostgreSQL 16 with pgvector
- 8GB+ RAM
- 20GB+ disk space
- Docker Desktop for containerized services
- Kind for local Kubernetes development

### Development Tools
- `sqlc` - SQL code generation
- `templ` - Template generation
- `golangci-lint` - Code quality checks
- `go-migrate` - Database migrations
- `air` - Live reload for development

## Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/yourusername/goassistant/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/goassistant/discussions)

## Architecture Highlights

- **Pure Go Standard Library**: No web frameworks, just `net/http` for clarity and control
- **Type-Safe Database Access**: Using sqlc for compile-time SQL verification
- **Advanced Error Handling**: Wrapped errors with full context propagation
- **Interface-Driven Design**: Clean boundaries between components
- **Agent-Based Architecture**: Autonomous agents for complex task execution
- **Vector-Based Memory**: Semantic search with PostgreSQL pgvector
- **Real-Time Streaming**: WebSocket support for responsive interactions
- **Comprehensive Observability**: Full tracing, metrics, and structured logging

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with the principles of idiomatic Go
- Powered by [LangChain Go](https://github.com/tmc/langchaingo) for agent capabilities
- Inspired by the need for a truly integrated development assistant
- Special thanks to the Go community and all contributors