# GoAssistant Architecture Document

## Executive Summary

GoAssistant is designed as a monolithic application with clear modular boundaries, built using idiomatic Go patterns without external web frameworks. The architecture prioritizes simplicity, maintainability, and performance while providing extensive integration capabilities.

## System Architecture

### High-Level Architecture

GoAssistant is designed as a comprehensive development platform that integrates AI assistance with specialized development tools. The architecture supports both conversational interactions and dedicated tool interfaces.

```
┌─────────────────────────────────────────────────┐
│             Presentation Layer                   │
│   (Web UI / Tool Interfaces / API Gateway)      │
├─────────────────────────────────────────────────┤
│             Application Layer                    │
│  (Tool Services / AI Orchestration / Router)    │
├─────────────────────────────────────────────────┤
│              Integration Layer                   │
│  (AI Providers / External APIs / Tool Engines)  │
├─────────────────────────────────────────────────┤
│               Data Layer                         │
│    (PostgreSQL / Cache / File Storage)          │
└─────────────────────────────────────────────────┘
```

### Core Components

#### 1. Platform Core
- **Purpose**: Central orchestration for the entire platform
- **Responsibilities**:
   - Request routing between different tool interfaces
   - User session management
   - Cross-tool state coordination
   - Unified authentication and authorization
   - Platform-wide search and navigation

#### 2. AI Service Layer
- **Purpose**: Provides intelligent assistance across all tools
- **Components**:
   - Agent Manager: Routes requests to appropriate AI agents
   - Context Manager: Maintains conversation and tool context
   - LangChain Integration: Orchestrates complex AI workflows
   - Model Gateway: Interfaces with Claude and Gemini

#### 3. Tool Services
Each tool operates as a semi-independent service with its own:
- **Business Logic**: Tool-specific operations and workflows
- **UI Components**: Dedicated interface optimized for the tool's purpose
- **Data Models**: Specialized data structures and storage
- **AI Integration**: Context-aware assistance specific to the tool

**Implemented Tools**:
- SQL Manager Service
- Kubernetes Dashboard Service
- Docker Manager Service
- Go Development Tools Service
- Cloudflare Integration Service
- Workspace Manager Service

#### 4. Shared Services
- **Authentication Service**: SSO across all tools
- **File Storage Service**: Unified file management
- **Notification Service**: Cross-tool alerts and updates
- **Analytics Service**: Usage tracking and insights
- **Search Service**: Platform-wide search capabilities

## Feature Specifications

### Platform Home Page

**Purpose**: Serves as the landing page and platform showcase

**Features**:
- Project introduction and value proposition
- Interactive tool gallery with live status
- Feature demonstrations with examples
- Quick access to recent work
- Platform statistics and achievements

**Technical Requirements**:
- Server-side rendered for SEO
- Progressive enhancement with HTMX
- Responsive grid layout
- Lazy loading for images and demos

### AI Chat System

**Purpose**: Conversational interface integrated with all platform tools

**Core Features**:
- Multi-agent system with specialized knowledge domains
- Seamless tool invocation from conversations
- Rich media support (code, tables, charts)
- Conversation branching and history
- Context preservation across tool switches

**Agent Types**:
1. **General Assistant**: Broad knowledge, routing to specialists
2. **SQL Expert**: Database schema understanding, query optimization
3. **DevOps Engineer**: Kubernetes, Docker, infrastructure
4. **Go Developer**: Code analysis, profiling, best practices
5. **Research Analyst**: Web search, documentation, synthesis

**Integration Points**:
- Can launch any tool with pre-populated context
- Receives results from tool operations
- Maintains awareness of user's current activity
- Provides contextual suggestions

### SQL Manager

**Purpose**: Complete database development environment

**Core Features**:
- Multi-database connection management
- Visual schema explorer with metadata
- Advanced query editor with IntelliSense
- Result set visualization and export
- Query optimization assistant

**Advanced Capabilities**:
- Visual query builder
- Database migration tools
- Performance monitoring dashboard
- Backup and restore operations
- Team collaboration on queries

**AI Integration**:
- Natural language to SQL conversion
- Query optimization suggestions
- Index recommendations
- Schema design assistance
- Performance troubleshooting

### Kubernetes Dashboard

**Purpose**: Comprehensive cluster management platform

**Core Features**:
- Multi-cluster support
- Real-time resource monitoring
- Workload management (deployments, pods, services)
- Configuration management (ConfigMaps, Secrets)
- Log aggregation and searching

**Advanced Capabilities**:
- YAML template library
- Deployment rollback and history
- Resource quota management
- Network policy visualization
- RBAC explorer

**AI Integration**:
- Deployment optimization
- Troubleshooting assistance
- Security recommendations
- Resource allocation suggestions
- Best practice enforcement

### Docker Manager

**Purpose**: Container lifecycle management

**Core Features**:
- Container monitoring and control
- Image registry browsing
- Volume and network management
- Docker Compose support
- Build process management

**Advanced Capabilities**:
- Multi-host management
- Container health monitoring
- Log streaming and analysis
- Resource usage tracking
- Security scanning

**AI Integration**:
- Dockerfile optimization
- Security vulnerability fixes
- Performance recommendations
- Compose file generation
- Troubleshooting guide

### Go Development Tools

**Purpose**: Integrated development environment for Go

**Core Features**:
- Project structure analysis
- Code quality metrics
- Performance profiling tools
- Test coverage visualization
- Dependency management

**Advanced Capabilities**:
- Real-time code analysis
- Memory and CPU profiling
- Goroutine visualization
- Race condition detection
- Benchmark management

**AI Integration**:
- Code review and suggestions
- Performance optimization tips
- Refactoring recommendations
- Documentation generation
- Bug prediction

### Cloudflare Integration

**Purpose**: Cloudflare service management

**Core Features**:
- Domain and DNS management
- Cloudflare Tunnel configuration
- R2 storage browser
- Workers script editor
- Analytics dashboard

**Advanced Capabilities**:
- Traffic routing rules
- Security policy management
- Performance optimization
- Cache management
- SSL certificate handling

**AI Integration**:
- Configuration optimization
- Security rule suggestions
- Performance tuning
- Cost optimization
- Incident response

### Workspace Manager

**Purpose**: Project and file organization

**Core Features**:
- Project creation and management
- File browser with preview
- Version control integration
- Team collaboration tools
- Resource sharing

**Advanced Capabilities**:
- Multi-project workspaces
- Template management
- Automated backups
- Access control
- Activity tracking

### Tool Integration Architecture

Each tool follows a consistent integration pattern:

1. **Standalone Interface**: Full-featured dedicated UI
2. **AI Chat Integration**: Callable from conversation
3. **Cross-tool Communication**: Share data and context
4. **Unified Authentication**: Single sign-on
5. **Consistent Navigation**: Platform-wide patterns

**Core Modules from langchaingo**:

1. **LLM Integration**
   - Support for multiple providers (OpenAI, Anthropic, Gemini, Ollama)
   - Unified interface via `llms.Model`
   - Streaming support
   - Token usage tracking

2. **Chains Architecture**
   - **LLMChain**: Basic prompt-to-completion chains
   - **ConversationalRetrievalQA**: RAG with conversation history
   - **APIChain**: For API integrations
   - **SQLDatabaseChain**: Database query chains
   - Custom chain composition

3. **Agent System**
   - **ZeroShotReactDescription**: ReAct framework agents
   - **ConversationalAgent**: Agents with memory
   - **Executor**: Iterative agent execution
   - Tool selection and orchestration
   - Error handling and retry logic

4. **Tool Integration**
   - Built-in tools: Calculator, SerpAPI, Perplexity
   - Custom tool interface implementation
   - Tool registry pattern
   - Async tool execution support

5. **Memory Management**
   - **ConversationBufferMemory**: Simple conversation history
   - **VectorStoreRetrieverMemory**: Semantic memory retrieval
   - **PostgreSQL-backed memory**: Using pgvector
   - Memory key management

6. **Vector Store Integration**
   - **PGVector**: Native PostgreSQL integration
   - **Chroma**: For development/testing
   - Document chunking and embedding
   - Similarity search with metadata filtering

7. **RAG Implementation**
   - Document loaders for various formats
   - Text splitters for chunking
   - Embedding generation pipeline
   - Retrieval chain with re-ranking

**Integration Architecture**:

```
┌─────────────────────────────────────────┐
│          GoAssistant Core               │
├─────────────────────────────────────────┤
│         LangChain Adapter Layer         │
│  ┌──────────┐  ┌──────────┐  ┌───────┐ │
│  │  Agents  │  │  Chains  │  │ Tools │ │
│  └──────────┘  └──────────┘  └───────┘ │
├─────────────────────────────────────────┤
│         langchaingo Library             │
└─────────────────────────────────────────┘
```

**Key Integration Points**:

1. **Unified Tool Interface**
   ```go
   type LangChainTool interface {
       Name() string
       Description() string 
       Call(ctx context.Context, input string) (string, error)
   }
   ```

2. **Agent Orchestration**
   - Dynamic tool selection based on query
   - Multi-step reasoning with intermediate results
   - Human-in-the-loop capability
   - Parallel tool execution

3. **Memory Strategy**
   - Short-term: In-memory conversation buffer
   - Long-term: PostgreSQL with pgvector
   - Semantic search for relevant context
   - Conversation summarization

4. **Chain Patterns**
   - Sequential chains for multi-step processes
   - Parallel chains for concurrent operations
   - Conditional chains based on outputs
   - Error recovery chains

**Usage Scenarios**:

1. **Code Generation Agent**
   - Uses Go AST tools + LLM
   - Iterative refinement
   - Test generation and validation

2. **Database Assistant Agent**
   - SQL generation and validation
   - Query optimization suggestions
   - Schema exploration

3. **DevOps Agent**
   - Kubernetes resource management
   - Log analysis and troubleshooting
   - Deployment automation

4. **Research Agent**
   - Multi-source information gathering
   - Fact checking and validation
   - Report generation

## Technical Requirements

### Core Technology Stack

**Go Version**: 1.24+ (required for latest stdlib features)

**Standard Library Usage**:
- **HTTP Server**: Pure `net/http` - no web frameworks
- **Logging**: `log/slog` for structured logging
- **Error Handling**: Error wrapping with `fmt.Errorf` and `%w` verb
- **Context**: Extensive use of `context.Context` for cancellation and values

**Essential Libraries**:
- **PostgreSQL Driver**: [`github.com/jackc/pgx/v5`](https://github.com/jackc/pgx)
- **LangChain Go**: [`github.com/tmc/langchaingo`](https://github.com/tmc/langchaingo)
- **Templ**: For type-safe HTML templates
- **HTMX**: For progressive enhancement (loaded from CDN)

**Development Tools**:
- **sqlc**: For type-safe SQL query generation
- **golangci-lint**: For code quality
- **go generate**: For code generation tasks

### Go Development Standards

1. **HTTP Server Implementation**
   - Use Go 1.24+ enhanced `net/http` package
   - Custom middleware chain implementation
   - No Gin, Echo, or other web frameworks
   - ServeMux with method-based routing
   - Proper context propagation

2. **Error Handling Pattern**
   - Use error wrapping: `fmt.Errorf("operation failed: %w", err)`
   - Create semantic error types
   - Check errors explicitly
   - Add context at each layer

3. **Logging with slog**
   - Structured logging throughout
   - Context-aware loggers
   - Log levels: Debug, Info, Warn, Error
   - Request ID propagation

4. **Code Organization**
   - Package by feature, not by layer
   - Accept interfaces, return structs
   - Composition over inheritance
   - Minimal external dependencies

5. **Database Access with pgx**
   - Use pgx native interface (not database/sql)
   - Connection pooling with pgxpool
   - Prepared statements
   - COPY protocol for bulk operations
   - Listen/Notify for real-time events

6. **Web Development Approach**
   - Server-side rendering with Templ
   - Progressive enhancement with HTMX
   - WebSocket for real-time features
   - No client-side JavaScript frameworks
   - Material Design 3 components from [templui.io](https://templui.io)

### Implementation Guidelines

#### HTTP Server Structure
- **Router**: Use `http.ServeMux` with pattern matching
- **Middleware**: Chain pattern using `func(http.Handler) http.Handler`
- **Handlers**: Method receivers on service structs
- **Request/Response**: JSON encoding/decoding with `encoding/json`
- **Static Files**: `http.FileServer` with embed for production

#### Error Handling Strategy
- **Wrapping**: Always use `fmt.Errorf` with `%w` for error context
- **Custom Errors**: Define domain-specific error types
- **HTTP Errors**: Structured error responses with appropriate status codes
- **Logging**: Log errors with full context using slog

#### Database Operations
- **Queries**: Generated by sqlc from SQL files
- **Transactions**: Explicit transaction management
- **Migrations**: SQL-based migrations with up/down support
- **Connection**: pgxpool for connection pooling
- **Monitoring**: pg_stat_statements integration

#### LangChain Integration
- **Agents**: Use langchaingo agent framework
- **Tools**: Implement langchain Tool interface
- **Memory**: PostgreSQL-backed conversation memory
- **Chains**: Composable chain patterns
- **Callbacks**: Monitoring and debugging hooks

### Web Interface Requirements

**Technology Stack**:
- Templ for type-safe templates
- HTMX for dynamic interactions
- WebSocket for real-time features
- Material Design 3 components
- No JavaScript frameworks

**Features**:
- Server-side rendering
- Progressive enhancement
- Real-time updates
- Responsive design
- Accessibility compliance

#### LangChain Integration Examples

**Tool Adapter Pattern**
Each GoAssistant tool will have a corresponding LangChain adapter that:
- Converts structured inputs to string format
- Handles response formatting
- Provides tool descriptions for agent selection
- Manages error translation

**Agent Composition**
Agents can be composed from multiple tools and chains:
- Development Agent: AST analyzer + Code generator + Test runner
- Database Agent: Query analyzer + Schema explorer + Performance advisor
- DevOps Agent: K8s client + Log analyzer + Metric collector

**Memory Strategies**
- **Immediate**: Last N messages in conversation
- **Relevant**: Vector similarity search for context
- **Summarized**: Compressed long-term memory
- **Structured**: Tool-specific memory (e.g., schema cache)

**Chain Orchestration**
Complex workflows through chain composition:
- Sequential execution with data passing
- Parallel chains for independent operations
- Conditional branching based on results
- Fallback chains for error recovery

## Observability Stack

1. **Metrics (Prometheus)**
   - Application metrics
   - Business metrics
   - SLI/SLO tracking
   - Custom dashboards

2. **Logging (Loki)**
   - Structured logging with slog
   - Log aggregation
   - Query capabilities
   - Alert integration

3. **Tracing (Jaeger)**
   - Distributed tracing
   - Latency analysis
   - Dependency mapping
   - Performance bottleneck identification

4. **OpenTelemetry Integration**
   - Unified instrumentation
   - Vendor-agnostic design
   - Auto-instrumentation where possible

## Deployment Architecture

### Local Development (Kind)

**Cluster Configuration**:
- Single control plane node
- Multiple worker nodes
- Port forwarding for services
- Volume mounts for development

**Required Services**:
- PostgreSQL with pgvector
- SearXNG
- Observability stack
- GoAssistant deployment

### Production Considerations

**Scalability**:
- Horizontal scaling capability
- Stateless application design
- Database connection pooling
- Cache layer implementation

**Security**:
- API key management
- RBAC implementation
- Network policies
- Secret rotation

**High Availability**:
- Health checks
- Graceful shutdown
- Circuit breakers
- Retry mechanisms

## LangChain Deep Integration Strategy

### Architecture Overview

The integration with langchaingo follows a modular approach where GoAssistant's native tools can be wrapped as LangChain tools, enabling sophisticated agent behaviors while maintaining clean separation of concerns.

### Core Components Integration

#### 1. LLM Provider Abstraction
```
GoAssistant Provider ←→ LangChain LLM Interface
├── Claude (Anthropic)
├── Gemini (Google)
└── Fallback Logic
```

#### 2. Tool Wrapping Strategy
All GoAssistant tools will implement both native and LangChain interfaces:
- **Native Interface**: Direct, type-safe Go integration
- **LangChain Interface**: String-based for agent compatibility
- **Automatic Conversion**: Middleware to bridge between interfaces

#### 3. Memory Architecture
- **Conversation Memory**: Buffer recent interactions
- **Semantic Memory**: Vector-based long-term storage
- **Tool Memory**: Cache tool execution results
- **User Memory**: Personalization and preferences

#### 4. Agent Types

**Development Assistant Agent**
- Tools: Go analyzer, profiler, code generator
- Chains: Code review → suggestion → implementation
- Memory: Project context and coding patterns

**Database Expert Agent**
- Tools: Query analyzer, schema explorer, performance advisor
- Chains: Query understanding → optimization → execution
- Memory: Schema cache and query history

**Infrastructure Agent**
- Tools: K8s client, Docker API, monitoring tools
- Chains: Issue detection → diagnosis → remediation
- Memory: Cluster state and incident history

**Research Agent**
- Tools: Web search, document analyzer, summarizer
- Chains: Query expansion → search → synthesis
- Memory: Research context and source tracking

### Implementation Phases

#### Phase 1: Foundation
- Wrap existing tools for LangChain compatibility
- Implement basic conversation memory
- Create simple ReAct agents

#### Phase 2: Advanced Agents
- Multi-agent collaboration
- Custom chain implementations
- Vector memory with pgvector

#### Phase 3: Optimization
- Agent performance tuning
- Caching strategies
- Parallel execution

### Best Practices

1. **Tool Design**
   - Keep tools focused and single-purpose
   - Provide clear descriptions for agent selection
   - Include examples in tool documentation

2. **Error Handling**
   - Graceful degradation when tools fail
   - Clear error messages for agent interpretation
   - Retry logic with exponential backoff

3. **Performance**
   - Cache expensive tool calls
   - Batch operations where possible
   - Stream responses for better UX

4. **Observability**
   - Trace agent decision making
   - Log tool usage and performance
   - Monitor token usage and costs

### Example Agent Workflows

**Debugging Workflow**
1. User reports issue
2. Agent analyzes error logs
3. Traces execution with pprof
4. Identifies bottleneck
5. Suggests optimization
6. Implements fix
7. Validates improvement

**Database Optimization Workflow**
1. User queries performance issue
2. Agent examines slow queries
3. Analyzes query plans
4. Checks index usage
5. Suggests optimizations
6. Tests improvements
7. Implements changes

**Infrastructure Troubleshooting**
1. Alert received
2. Agent checks metrics
3. Analyzes logs
4. Correlates events
5. Identifies root cause
6. Executes remediation
7. Monitors recovery

## Data Flow and Integration Points

### Request Processing Flow

The platform handles different types of requests through specialized paths:

**Tool Interface Requests**
When users interact with tool interfaces, requests flow directly to the specific tool service. Each tool maintains its own request handlers optimized for its operations. The tool service may invoke AI assistance as needed, maintaining context about the current operation. Results update the UI in real-time through HTMX partial updates.

**AI Chat Requests**
Chat interactions route through the AI orchestration layer, which determines the appropriate agent based on context and content. Agents can invoke tools programmatically, receiving structured results that enhance their responses. The chat interface maintains WebSocket connections for streaming responses.

**Cross-Tool Operations**
When operations span multiple tools, the platform core coordinates the workflow. For example, analyzing a slow database query might trigger the SQL Manager to examine the query plan, the Kubernetes Dashboard to check pod resources, and the monitoring system to correlate performance metrics. Results aggregate in a unified view with AI-synthesized insights.

### State Management Architecture

**User Session State**
Each user session maintains state across all platform tools. This includes current workspace, active connections, open files, and navigation history. State persists to PostgreSQL with Redis caching for performance. WebSocket connections synchronize state across multiple browser tabs.

**Tool-Specific State**
Individual tools manage their operational state, such as database connections, Kubernetes contexts, or profiling sessions. State isolation ensures tool independence while shared interfaces enable cross-tool data flow when needed.

**AI Context Management**
The AI system maintains conversation history and tool interaction context. This context transfers seamlessly when users switch between chat and tool interfaces. Vector embeddings of important interactions enable semantic search across historical actions.

### Integration Architecture

**Internal Integration**
Tools communicate through well-defined service interfaces. Event bus patterns enable loose coupling between services. Shared libraries provide common functionality without duplicating code. PostgreSQL serves as the single source of truth for persistent data.

**External Integration**
Each external service integration follows adapter patterns for flexibility. Connection pooling and circuit breakers ensure reliability. Credentials store securely with encryption at rest. Rate limiting prevents API abuse while maintaining responsive user experience.

**AI Model Integration**
The platform abstracts AI providers behind a unified interface. Request routing intelligently selects models based on task requirements. Fallback chains ensure availability even if primary models fail. Token usage tracking enables cost management and optimization.

## Security Architecture

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- API key management
- Session handling

### Data Protection
- Encryption at rest
- TLS for all communications
- Sensitive data masking
- Audit logging

### Compliance Considerations
- GDPR compliance for user data
- API usage tracking
- Data retention policies
- Right to deletion

## Performance Requirements

### Response Time Targets
- CLI commands: < 100ms overhead
- Web UI interactions: < 200ms
- AI responses: < 5s for simple queries
- Search operations: < 2s

### Resource Utilization
- Memory: < 500MB baseline
- CPU: Efficient goroutine usage
- Database connections: Pooled and limited
- Network: Optimized payload sizes

## Testing Strategy

### Test Categories
1. **Unit Tests**
   - Business logic validation
   - Error handling verification
   - Interface contract testing

2. **Integration Tests**
   - Database operations
   - External API interactions
   - Tool execution flows

3. **End-to-End Tests**
   - User journey validation
   - Performance benchmarks
   - Load testing

### Quality Metrics
- Code coverage > 80%
- All critical paths tested
- Performance regression detection
- Security vulnerability scanning

## Go Best Practices References

This project follows established Go best practices as documented in:

1. **[Effective Go](https://go.dev/doc/effective_go)** - Core Go idioms
2. **[Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)** - Google's Go style guide
3. **[Standard Package Layout](https://go.dev/doc/modules/layout)** - Package organization
4. **[Go Proverbs](https://go-proverbs.github.io/)** - Guiding principles
5. **Standard Library Examples** - Learn from Go's own source code

Key principles applied:
- "Accept interfaces, return structs"
- "The bigger the interface, the weaker the abstraction"
- "Make the zero value useful"
- "Errors are values"
- "Don't panic"
- "Concurrency is not parallelism"
- "Share memory by communicating"

## Migration and Upgrade Strategy

### Database Migrations
- Version-controlled migrations
- Rollback capabilities
- Zero-downtime migrations
- Data validation

### Application Updates
- Backward compatibility
- Feature flags
- Gradual rollout
- Health monitoring

## Future Extensibility

### Plugin System Design
- Dynamic tool loading
- Version compatibility
- Dependency management
- Security sandboxing

### API Gateway Potential
- Rate limiting
- API versioning
- Request routing
- Analytics collection

### Multi-tenancy Considerations
- User isolation
- Resource quotas
- Billing integration
- Custom configurations