package templates

import (
	"github.com/koopa0/assistant-go/internal/web/i18n"
)

// SettingsPageData represents the data for the settings page
type SettingsPageData struct {
	AppLayoutData
	UserProfile UserProfile
	APIKeys     []SettingsAPIKey
}

type UserProfile struct {
	ID            string
	Name          string
	Email         string
	Avatar        string
	Role          string
	JoinDate      string
	LastActive    string
	Stats         UserStats
	Preferences   UserPreferences
	Language      string
	Theme         string
	Notifications bool
}

type SettingsAPIKey struct {
	ID          string
	Name        string
	Provider    string
	LastUsed    string
	ExpiresAt   string
	IsActive    bool
	MaskedValue string
}

// SettingsPage implements the settings page based on ui.md specifications
templ SettingsPage(data SettingsPageData) {
	@AppLayout(data.AppLayoutData, settingsPageContent(data))
}

templ settingsPageContent(data SettingsPageData) {
	<div class="settings-container">
		<div class="settings-header">
			<h1 class="page-title">{ i18n.T("settings.title", data.Lang) }</h1>
			<p class="page-subtitle">{ i18n.T("settings.subtitle", data.Lang) }</p>
		</div>
		
		<div class="settings-layout">
			<!-- Settings Navigation -->
			<nav class="settings-nav">
				<a href="#profile" class="settings-nav-item active">
					<span class="material-symbols-rounded">person</span>
					{ i18n.T("settings.profile", data.Lang) }
				</a>
				<a href="#preferences" class="settings-nav-item">
					<span class="material-symbols-rounded">settings</span>
					{ i18n.T("settings.preferences", data.Lang) }
				</a>
				<a href="#api-keys" class="settings-nav-item">
					<span class="material-symbols-rounded">key</span>
					{ i18n.T("settings.api_keys", data.Lang) }
				</a>
				<a href="#privacy" class="settings-nav-item">
					<span class="material-symbols-rounded">security</span>
					{ i18n.T("settings.privacy", data.Lang) }
				</a>
				<a href="#data" class="settings-nav-item">
					<span class="material-symbols-rounded">storage</span>
					{ i18n.T("settings.data_management", data.Lang) }
				</a>
			</nav>
			
			<!-- Settings Content -->
			<div class="settings-content">
				<!-- Profile Section -->
				<section id="profile" class="settings-section">
					<h2 class="section-title">{ i18n.T("settings.profile", data.Lang) }</h2>
					<div class="profile-form">
						<div class="profile-avatar-section">
							<div class="avatar-preview">
								if data.UserProfile.Avatar != "" {
									<img src={ data.UserProfile.Avatar } alt="Avatar" class="avatar-image"/>
								} else {
									<div class="avatar-placeholder">
										<span class="material-symbols-rounded">person</span>
									</div>
								}
							</div>
							<button class="btn btn-secondary">
								{ i18n.T("settings.change_avatar", data.Lang) }
							</button>
						</div>
						
						<form class="profile-fields">
							<div class="input-group">
								<input type="text" id="name" class="input" value={ data.UserProfile.Name } placeholder=" "/>
								<label for="name" class="input-label">{ i18n.T("settings.display_name", data.Lang) }</label>
							</div>
							
							<div class="input-group">
								<input type="email" id="email" class="input" value={ data.UserProfile.Email } placeholder=" "/>
								<label for="email" class="input-label">{ i18n.T("settings.email", data.Lang) }</label>
							</div>
							
							<div class="input-group">
								<input type="password" id="password" class="input" placeholder=" "/>
								<label for="password" class="input-label">{ i18n.T("settings.new_password", data.Lang) }</label>
							</div>
							
							<button type="submit" class="btn btn-primary">
								{ i18n.T("action.save", data.Lang) }
							</button>
						</form>
					</div>
				</section>
				
				<!-- Preferences Section -->
				<section id="preferences" class="settings-section">
					<h2 class="section-title">{ i18n.T("settings.preferences", data.Lang) }</h2>
					<div class="preferences-grid">
						<div class="preference-item">
							<label for="language-select">{ i18n.T("settings.language", data.Lang) }</label>
							<select id="language-select" class="select" onchange="switchLanguage(this.value)" value={ data.UserProfile.Language }>
								<option value="en" selected?={ data.UserProfile.Language == "en" }>English</option>
								<option value="zh-TW" selected?={ data.UserProfile.Language == "zh-TW" }>繁體中文</option>
							</select>
						</div>
						
						<div class="preference-item">
							<label for="theme-select">{ i18n.T("settings.theme", data.Lang) }</label>
							<select id="theme-select" class="select" onchange="changeTheme(this.value)" value={ data.UserProfile.Theme }>
								<option value="light" selected?={ data.UserProfile.Theme == "light" }>Light</option>
								<option value="dark" selected?={ data.UserProfile.Theme == "dark" }>Dark</option>
								<option value="system" selected?={ data.UserProfile.Theme == "system" }>System</option>
							</select>
						</div>
						
						<div class="preference-item full-width">
							<label class="switch-label">
								<input type="checkbox" class="switch" checked?={ data.UserProfile.Notifications }/>
								<span class="switch-slider"></span>
								{ i18n.T("settings.enable_notifications", data.Lang) }
							</label>
						</div>
					</div>
				</section>
				
				<!-- API Keys Section -->
				<section id="api-keys" class="settings-section">
					<div class="section-header">
						<h2 class="section-title">{ i18n.T("settings.api_keys", data.Lang) }</h2>
						<button class="btn btn-primary" onclick="showAddAPIKeyModal()">
							<span class="material-symbols-rounded">add</span>
							{ i18n.T("settings.add_api_key", data.Lang) }
						</button>
					</div>
					
					<div class="api-keys-list">
						for _, key := range data.APIKeys {
							@APIKeyItem(key, data.Lang)
						}
					</div>
				</section>
				
				<!-- Privacy Section -->
				<section id="privacy" class="settings-section">
					<h2 class="section-title">{ i18n.T("settings.privacy", data.Lang) }</h2>
					<div class="privacy-options">
						<label class="switch-label">
							<input type="checkbox" class="switch" checked/>
							<span class="switch-slider"></span>
							{ i18n.T("settings.share_usage_data", data.Lang) }
						</label>
						
						<label class="switch-label">
							<input type="checkbox" class="switch"/>
							<span class="switch-slider"></span>
							{ i18n.T("settings.private_mode", data.Lang) }
						</label>
						
						<div class="privacy-info">
							<p>{ i18n.T("settings.privacy_info", data.Lang) }</p>
						</div>
					</div>
				</section>
				
				<!-- Data Management Section -->
				<section id="data" class="settings-section">
					<h2 class="section-title">{ i18n.T("settings.data_management", data.Lang) }</h2>
					<div class="data-actions">
						<button class="btn btn-secondary">
							<span class="material-symbols-rounded">download</span>
							{ i18n.T("settings.export_data", data.Lang) }
						</button>
						
						<button class="btn btn-secondary">
							<span class="material-symbols-rounded">history</span>
							{ i18n.T("settings.clear_history", data.Lang) }
						</button>
						
						<button class="btn btn-text danger">
							<span class="material-symbols-rounded">delete_forever</span>
							{ i18n.T("settings.delete_account", data.Lang) }
						</button>
					</div>
				</section>
			</div>
		</div>
	</div>
	
	<script>
		// Handle settings navigation
		document.querySelectorAll('.settings-nav-item').forEach(item => {
			item.addEventListener('click', (e) => {
				e.preventDefault();
				
				// Update active state
				document.querySelectorAll('.settings-nav-item').forEach(i => i.classList.remove('active'));
				item.classList.add('active');
				
				// Show corresponding section
				const target = item.getAttribute('href');
				document.querySelectorAll('.settings-section').forEach(section => {
					section.style.display = section.id === target.substring(1) ? 'block' : 'none';
				});
			});
		});
		
		// Theme changing function
		function changeTheme(theme) {
			let actualTheme = theme;
			
			// Handle system theme
			if (theme === 'system') {
				actualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
			}
			
			// Add transition class for smooth theme switching
			document.documentElement.classList.add('theme-transitioning');
			
			// Apply new theme
			document.documentElement.setAttribute('data-theme', actualTheme);
			document.body.setAttribute('data-theme', actualTheme);
			localStorage.setItem('theme', theme); // Store original choice (light/dark/system)
			
			// Remove transition class after animation completes
			setTimeout(() => {
				document.documentElement.classList.remove('theme-transitioning');
			}, 400);
			
			// Send theme change to server
			fetch('/api/preferences/theme', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ theme: theme })
			}).catch(error => {
				console.warn('Failed to save theme preference:', error);
			});
		}
		
		function showAddAPIKeyModal() {
			// TODO: Implement modal
			console.log('Add API Key modal');
		}
	</script>
}

templ APIKeyItem(key SettingsAPIKey, lang string) {
	<div class="api-key-item" data-key-id={ key.ID }>
		<div class="api-key-info">
			<div class="api-key-header">
				<h4 class="api-key-name">{ key.Name }</h4>
				<span class={ "api-key-status", templ.KV("active", key.IsActive), templ.KV("inactive", !key.IsActive) }>
					if key.IsActive {
						{ i18n.T("status.active", lang) }
					} else {
						{ i18n.T("status.inactive", lang) }
					}
				</span>
			</div>
			<div class="api-key-details">
				<span class="api-key-provider">{ key.Provider }</span>
				<span class="api-key-value">{ key.MaskedValue }</span>
				<span class="api-key-meta">
					{ i18n.T("tools.last_used", lang) }: { key.LastUsed }
					if key.ExpiresAt != "" {
						· { i18n.T("settings.expires", lang) }: { key.ExpiresAt }
					}
				</span>
			</div>
		</div>
		<div class="api-key-actions">
			<button class="btn btn-text" data-key-id={ key.ID } onclick="editAPIKey(this.dataset.keyId)">
				<span class="material-symbols-rounded">edit</span>
			</button>
			<button class="btn btn-text danger" data-key-id={ key.ID } onclick="deleteAPIKey(this.dataset.keyId)">
				<span class="material-symbols-rounded">delete</span>
			</button>
		</div>
	</div>
	
	<script>
		function editAPIKey(keyId) {
			// TODO: Implement edit
			console.log('Edit API Key:', keyId);
		}
		
		function deleteAPIKey(keyId) {
			if (confirm('Are you sure you want to delete this API key?')) {
				// TODO: Implement delete
				console.log('Delete API Key:', keyId);
			}
		}
	</script>
}