package templates


// ChatPage implements the chat interface based on ui.md specifications
templ ChatPage(data ChatPageData) {
	@AppLayout(data.AppLayoutData, chatPageContent(data))
}

templ chatPageContent(data ChatPageData) {
	<!-- Modern Chat Container -->
	<div class="chat-container">
		<!-- Chat Sidebar -->
		<aside class="chat-sidebar">
			<!-- New Chat Button -->
			<div class="sidebar-header">
				<button 
					class="btn-new-chat"
					hx-post="/api/chat/new"
					hx-target="#messages"
					hx-swap="innerHTML"
				>
					<span class="material-symbols-rounded">add</span>
					<span>新對話</span>
				</button>
			</div>
			
			<!-- Chat History -->
			<div class="chat-history">
				<h3 class="history-title">對話記錄</h3>
				<div class="history-list">
					for _, chat := range data.RecentChats {
						<div class="history-item">
							<div class="history-content">
								<h4 class="history-item-title">{ chat.Title }</h4>
								<p class="history-item-preview">{ chat.Preview }</p>
								<span class="history-item-time">{ chat.Time }</span>
							</div>
						</div>
					}
				</div>
			</div>
		</aside>
		
		<!-- Main Chat Area -->
		<div class="chat-main">
			<!-- Chat Header -->
			<div class="chat-header">
				<div class="chat-header-info">
					<div class="ai-avatar">
						<span class="material-symbols-rounded">psychology</span>
					</div>
					<div class="ai-info">
						<h1 class="ai-name">GoAssistant</h1>
						<p class="ai-status">AI開發助手 · 在線</p>
					</div>
				</div>
				<div class="chat-header-actions">
					<button class="header-action-btn">
						<span class="material-symbols-rounded">settings</span>
					</button>
				</div>
			</div>
			
			<!-- Messages Area -->
			<div class="messages-container" id="messages">
				if len(data.Messages) == 0 {
					<!-- Welcome Screen -->
					<div class="welcome-screen">
						<div class="welcome-avatar">
							<div class="avatar-glow"></div>
							<div class="avatar-icon">
								<span class="material-symbols-rounded">psychology</span>
							</div>
						</div>
						<h2 class="welcome-title">你好！我是GoAssistant</h2>
						<p class="welcome-subtitle">我是你的專業AI開發助手，可以幫助你處理Go語言開發、資料庫管理和DevOps運維等工作</p>
						
						<!-- Quick Actions -->
						<div class="quick-actions">
							<button class="quick-action" data-prompt="幫我分析這段Go程式碼">
								<span class="material-symbols-rounded">code</span>
								<span>程式碼分析</span>
							</button>
							<button class="quick-action" data-prompt="最佳化這個SQL查詢">
								<span class="material-symbols-rounded">database</span>
								<span>SQL優化</span>
							</button>
							<button class="quick-action" data-prompt="幫我設定Kubernetes部署">
								<span class="material-symbols-rounded">cloud</span>
								<span>K8s部署</span>
							</button>
							<button class="quick-action" data-prompt="檢查我的Docker配置">
								<span class="material-symbols-rounded">memory</span>
								<span>Docker配置</span>
							</button>
						</div>
					</div>
				} else {
					<!-- Message List -->
					<div class="messages-list">
						for _, msg := range data.Messages {
							@modernChatMessage(msg, data.Lang)
						}
					</div>
				}
			</div>
			
			<!-- Chat Input -->
			<div class="chat-input-area">
				<form 
					class="chat-form"
					hx-post="/api/chat/send"
					hx-target="#messages"
					hx-swap="beforeend"
				>
					<input type="hidden" name="agent_id" value={ data.SelectedAgentID } />
					
					<div class="input-container">
						<button type="button" class="attach-btn">
							<span class="material-symbols-rounded">attach_file</span>
						</button>
						
						<textarea 
							name="message"
							class="message-input"
							placeholder="輸入你的問題..."
							rows="1"
							required
						></textarea>
						
						<button type="submit" class="send-btn">
							<span class="material-symbols-rounded">send</span>
						</button>
					</div>
					
					<div class="input-hints">
						<span class="hint-text">按Enter送出，Shift+Enter換行</span>
						<span class="char-count">0/2000</span>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	@chatScript()
}

// ChatAgentCard renders an agent selection card
templ ChatAgentCard(id, name, description, icon string, isActive bool) {
	<div 
		class={
			"p-4 rounded-lg border cursor-pointer transition-all",
			templ.KV("border-primary bg-primary/10", isActive),
			templ.KV("border-outline-variant hover:border-primary hover:bg-surface-light", !isActive)
		}
		data-agent-id={ id }
		onclick="selectAgent(this.dataset.agentId)"
	>
		<div class="flex items-start gap-3">
			<div class={
				"w-10 h-10 rounded-lg flex items-center justify-center",
				templ.KV("bg-primary text-white", isActive),
				templ.KV("bg-surface-variant text-text-secondary", !isActive)
			}>
				<span class="material-symbols-rounded">{ icon }</span>
			</div>
			<div class="flex-1">
				<h4 class="font-medium text-text-primary">{ name }</h4>
				<p class="text-sm text-text-secondary mt-1">{ description }</p>
			</div>
		</div>
	</div>
}

// PromptSuggestion renders a suggested prompt
templ PromptSuggestion(text, icon string) {
	<button 
		class="p-3 rounded-lg border border-outline-variant hover:border-primary hover:bg-surface-light text-left transition-all flex items-center gap-3"
		data-prompt={ text }
		onclick="setPrompt(this.dataset.prompt)"
	>
		<span class="material-symbols-rounded text-text-secondary">{ icon }</span>
		<span class="text-sm text-text-primary">{ text }</span>
	</button>
}

// renderMessage renders a chat message
templ renderMessage(msg ChatMessage, lang string) {
	<div class={
		"flex gap-3",
		templ.KV("justify-end", msg.Role == "user"),
		templ.KV("justify-start", msg.Role != "user")
	}>
		if msg.Role != "user" {
			<div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
				<span class="material-symbols-rounded text-white text-sm">auto_awesome</span>
			</div>
		}
		
		<div class={
			"max-w-[80%] rounded-lg p-4",
			templ.KV("bg-primary text-white", msg.Role == "user"),
			templ.KV("bg-surface-light text-text-primary", msg.Role != "user")
		}>
			<div class="whitespace-pre-wrap">{ msg.Content }</div>
			<div class="text-xs opacity-60 mt-2">{ msg.Timestamp }</div>
		</div>
		
		if msg.Role == "user" {
			<div class="w-8 h-8 bg-surface-variant rounded-lg flex items-center justify-center flex-shrink-0">
				<span class="material-symbols-rounded text-text-secondary text-sm">person</span>
			</div>
		}
	</div>
}

// renderToolOutput renders tool execution output
templ renderToolOutput(output ToolOutput) {
	<div class="border border-outline-variant rounded-lg p-4">
		<div class="flex items-center gap-2 mb-3">
			<span class="material-symbols-rounded text-primary">{ output.Icon }</span>
			<h4 class="font-medium text-text-primary">{ output.ToolName }</h4>
		</div>
		
		<div class="text-sm text-text-secondary mb-2">
			Input: <code class="bg-surface px-1 rounded">{ output.Input }</code>
		</div>
		
		<div class="bg-surface rounded p-3 mt-3">
			<pre class="text-sm text-text-primary whitespace-pre-wrap">{ output.Result }</pre>
		</div>
		
		if output.ActionURL != "" {
			<button 
				class="btn btn-outline btn-sm mt-3"
				data-url={ output.ActionURL }
				onclick="window.open(this.dataset.url, '_blank')"
			>
				Open in { output.ToolName }
			</button>
		}
	</div>
}

// modernChatMessage renders a modern chat message
templ modernChatMessage(msg ChatMessage, lang string) {
	<div class="message-wrapper">
		if msg.Role == "user" {
			<div class="message user-message">
				<div class="message-content">{ msg.Content }</div>
				<div class="message-time">{ msg.Timestamp }</div>
				<div class="message-avatar">
					<span class="material-symbols-rounded">person</span>
				</div>
			</div>
		} else {
			<div class="message ai-message">
				<div class="message-avatar">
					<span class="material-symbols-rounded">psychology</span>
				</div>
				<div class="message-content">{ msg.Content }</div>
				<div class="message-time">{ msg.Timestamp }</div>
			</div>
		}
	</div>
}

// chatScript renders the JavaScript for chat functionality
script chatScript() {
	document.addEventListener('DOMContentLoaded', function() {
		// Auto-resize textarea
		const textarea = document.querySelector('textarea[name="message"]');
		if (textarea) {
			textarea.addEventListener('input', function() {
				this.style.height = 'auto';
				this.style.height = Math.min(this.scrollHeight, 200) + 'px';
			});
			
			// Submit on Enter (without Shift)
			textarea.addEventListener('keydown', function(e) {
				if (e.key === 'Enter' && !e.shiftKey) {
					e.preventDefault();
					this.closest('form').requestSubmit();
				}
			});
		}
		
		// Agent selection
		window.selectAgent = function(agentId) {
			// Update hidden input
			const input = document.querySelector('input[name="agent_id"]');
			if (input) input.value = agentId;
			
			// Update visual state
			document.querySelectorAll('[data-agent-id]').forEach(card => {
				const isSelected = card.dataset.agentId === agentId;
				if (isSelected) {
					card.classList.add('border-primary', 'bg-primary/10');
					card.classList.remove('border-outline-variant');
				} else {
					card.classList.remove('border-primary', 'bg-primary/10');
					card.classList.add('border-outline-variant');
				}
			});
		};
		
		// Set prompt in textarea
		window.setPrompt = function(prompt) {
			const textarea = document.querySelector('textarea[name="message"]');
			if (textarea) {
				textarea.value = prompt;
				textarea.focus();
				textarea.dispatchEvent(new Event('input'));
			}
		};
		
		// Scroll to bottom on new messages
		const messagesContainer = document.getElementById('messages');
		if (messagesContainer) {
			const observer = new MutationObserver(() => {
				messagesContainer.scrollTop = messagesContainer.scrollHeight;
			});
			observer.observe(messagesContainer, { childList: true, subtree: true });
		}
	});
}

// ChatPageData represents the data for the chat page
type ChatPageData struct {
	AppLayoutData
	Messages        []ChatMessage
	RecentChats     []ChatItem
	SelectedAgentID string
	ShowToolPanel   bool
	ToolOutputs     []ToolOutput
}

type ChatMessage struct {
	ID        string
	Role      string // "user" or "assistant"
	Content   string
	Timestamp string
}

type ChatItem struct {
	ID       string
	Title    string
	Preview  string
	Time     string
	IsActive bool
}

type ToolOutput struct {
	ToolName  string
	Icon      string
	Input     string
	Result    string
	ActionURL string
}