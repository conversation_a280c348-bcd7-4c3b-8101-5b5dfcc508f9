package templates

import (
	"fmt"
)

type HomePageData struct {
	AppLayoutData
	UserName    string
	RecentChats []RecentChat
	ActiveTools []ActiveTool
}

type RecentChat struct {
	ID      string
	Title   string
	Preview string
	Time    string
}

type ActiveTool struct {
	Name     string
	Status   string
	LastUsed string
}

// HomePage implements the home page with modern AI assistant feel
templ HomePage(data HomePageData) {
	@AppLayout(data.AppLayoutData, homePageContent(data))
}

templ homePageContent(data HomePageData) {
	<!-- AI Assistant Hero Section -->
	<section class="hero-section">
		<div class="hero-background">
			<div class="hero-pattern"></div>
		</div>
		<div class="hero-content">
			<div class="hero-avatar">
				<div class="avatar-glow"></div>
				<div class="avatar-core">
					<span class="material-symbols-rounded">psychology</span>
				</div>
			</div>
			<h1 class="hero-title">
				你好！我是 <span class="hero-name">GoAssistant</span>
			</h1>
			<p class="hero-subtitle">
				你的專業AI開發助手，精通Go語言開發、資料庫管理和DevOps運維
			</p>
			<div class="hero-stats">
				<div class="stat-item">
					<span class="stat-number">10K+</span>
					<span class="stat-label">問題解決</span>
				</div>
				<div class="stat-item">
					<span class="stat-number">50+</span>
					<span class="stat-label">專業工具</span>
				</div>
				<div class="stat-item">
					<span class="stat-number">24/7</span>
					<span class="stat-label">隨時服務</span>
				</div>
			</div>
			<div class="hero-actions">
				<a href="/chat" class="btn-primary-large">
					<span class="material-symbols-rounded">chat</span>
					<span>開始對話</span>
				</a>
				<a href="/tools" class="btn-secondary-large">
					<span class="material-symbols-rounded">build</span>
					<span>探索工具</span>
				</a>
			</div>
		</div>
	</section>

	<!-- AI Capabilities Section -->
	<section class="capabilities-section">
		<div class="container mx-auto px-4">
			<div class="section-header">
				<h2 class="section-title">AI智能能力</h2>
				<p class="section-subtitle">專業級AI助手，為你的開發工作提供全方位支援</p>
			</div>
			<div class="capabilities-grid">
				@AICapabilityCard("smart_toy", "智能對話", "自然語言交互，理解你的需求並提供專業建議", "對話式AI", "var(--primary)")
				@AICapabilityCard("auto_fix_high", "程式碼分析", "深度分析Go程式碼，發現問題並提供優化方案", "靜態分析", "var(--go-development)")
				@AICapabilityCard("storage", "資料庫優化", "SQL查詢優化、架構設計和效能調優", "資料庫專家", "var(--sql-manager)")
				@AICapabilityCard("cloud", "DevOps自動化", "容器編排、CI/CD流程和雲端部署", "運維專家", "var(--kubernetes)")
			</div>
		</div>
	</section>

	<!-- Professional Tools Section -->
	<section class="tools-section">
		<div class="container mx-auto px-4">
			<div class="section-header">
				<h2 class="section-title">專業開發工具</h2>
				<p class="section-subtitle">整合多種開發工具，提升你的工作效率</p>
			</div>
			<div class="tools-showcase">
				@ProfessionalToolCard("sql-manager", "SQL Manager", "智能資料庫管理和查詢優化", "database", "1,234次使用", "98%成功率")
				@ProfessionalToolCard("kubernetes", "Kubernetes Dashboard", "容器編排和叢集監控", "cloud", "856個專案", "99%可用性")
				@ProfessionalToolCard("go-development", "Go開發套件", "程式碼分析、測試和部署", "code", "3,567個專案", "95%效能提升")
				@ProfessionalToolCard("docker", "Docker Manager", "容器生命週期管理", "memory", "2,341個容器", "97%穩定性")
				@ProfessionalToolCard("cloudflare", "Cloudflare整合", "CDN和安全服務", "language", "678個網站", "99.9%正常運行")
				@ProfessionalToolCard("monitoring", "智能監控", "系統效能和告警管理", "monitoring", "24/7監控", "秒級響應")
			</div>
		</div>
	</section>

	<!-- Recent Activity Dashboard -->
	if len(data.RecentChats) > 0 || len(data.ActiveTools) > 0 {
		<section class="activity-section">
			<div class="container mx-auto px-4">
				<div class="section-header">
					<h2 class="section-title">工作動態</h2>
					<p class="section-subtitle">查看你的最近活動和進行中的工作</p>
				</div>
				<div class="activity-dashboard">
					<!-- Recent Conversations -->
					if len(data.RecentChats) > 0 {
						<div class="activity-card">
							<div class="activity-header">
								<div class="activity-icon chat-icon">
									<span class="material-symbols-rounded">chat</span>
								</div>
								<div class="activity-info">
									<h3 class="activity-title">最近對話</h3>
									<p class="activity-subtitle">{ fmt.Sprintf("%d個對話", len(data.RecentChats)) }</p>
								</div>
								<a href="/chat" class="activity-action">查看全部</a>
							</div>
							<div class="activity-list">
								for _, chat := range data.RecentChats {
									@RecentConversationItem(chat.Title, chat.Preview, chat.Time)
								}
							</div>
						</div>
					}
					
					<!-- Active Tools -->
					if len(data.ActiveTools) > 0 {
						<div class="activity-card">
							<div class="activity-header">
								<div class="activity-icon tools-icon">
									<span class="material-symbols-rounded">build</span>
								</div>
								<div class="activity-info">
									<h3 class="activity-title">活躍工具</h3>
									<p class="activity-subtitle">{ fmt.Sprintf("%d個工具運行中", len(data.ActiveTools)) }</p>
								</div>
								<a href="/tools" class="activity-action">管理工具</a>
							</div>
							<div class="activity-list">
								for _, tool := range data.ActiveTools {
									@ActiveToolStatus(tool.Name, tool.Status, tool.LastUsed)
								}
							</div>
						</div>
					}
				</div>
			</div>
		</section>
	}

	<!-- Quick Start Section -->
	<section class="quickstart-section">
		<div class="container mx-auto px-4">
			<div class="quickstart-card">
				<div class="quickstart-content">
					<h2 class="quickstart-title">準備開始你的AI開發之旅？</h2>
					<p class="quickstart-subtitle">立即體驗GoAssistant的強大功能，讓AI成為你的開發夥伴</p>
					<div class="quickstart-actions">
						<a href="/chat" class="btn-primary-large">
							<span class="material-symbols-rounded">rocket_launch</span>
							<span>立即開始</span>
						</a>
						<a href="/tools" class="btn-outline-large">
							<span class="material-symbols-rounded">explore</span>
							<span>探索功能</span>
						</a>
					</div>
				</div>
				<div class="quickstart-visual">
					<div class="visual-elements">
						<div class="floating-element element-1">
							<span class="material-symbols-rounded">code</span>
						</div>
						<div class="floating-element element-2">
							<span class="material-symbols-rounded">database</span>
						</div>
						<div class="floating-element element-3">
							<span class="material-symbols-rounded">cloud</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
}

// AICapabilityCard renders an AI capability card
templ AICapabilityCard(icon, title, description, tag, color string) {
	<div class="capability-card">
		<div class="capability-icon" style={ fmt.Sprintf("background: %s;", color) }>
			<span class="material-symbols-rounded">{ icon }</span>
		</div>
		<div class="capability-content">
			<div class="capability-tag">{ tag }</div>
			<h3 class="capability-title">{ title }</h3>
			<p class="capability-description">{ description }</p>
		</div>
	</div>
}

// ProfessionalToolCard renders a professional tool card
templ ProfessionalToolCard(id, name, description, icon, usage, performance string) {
	<div class="tool-showcase-card" data-tool-id={ id }>
		<div class="tool-header">
			<div class="tool-showcase-icon">
				<span class="material-symbols-rounded">{ icon }</span>
			</div>
			<div class="tool-status">
				<span class="status-indicator active"></span>
				<span class="status-text">運行中</span>
			</div>
		</div>
		<div class="tool-content">
			<h3 class="tool-name">{ name }</h3>
			<p class="tool-description">{ description }</p>
		</div>
		<div class="tool-metrics">
			<div class="metric-item">
				<span class="metric-label">使用量</span>
				<span class="metric-value">{ usage }</span>
			</div>
			<div class="metric-item">
				<span class="metric-label">效能</span>
				<span class="metric-value">{ performance }</span>
			</div>
		</div>
		<div class="tool-actions">
			<a href={ templ.URL(fmt.Sprintf("/tools/%s", id)) } class="btn-tool-primary">
				<span class="material-symbols-rounded">open_in_new</span>
				<span>開啟工具</span>
			</a>
		</div>
	</div>
}

// RecentConversationItem renders a recent conversation item
templ RecentConversationItem(title, preview, time string) {
	<div class="conversation-item">
		<div class="conversation-avatar">
			<span class="material-symbols-rounded">chat</span>
		</div>
		<div class="conversation-content">
			<h4 class="conversation-title">{ title }</h4>
			<p class="conversation-preview">{ preview }</p>
			<span class="conversation-time">{ time }</span>
		</div>
		<div class="conversation-actions">
			<button class="action-btn-small">
				<span class="material-symbols-rounded">arrow_forward</span>
			</button>
		</div>
	</div>
}

// ActiveToolStatus renders an active tool status item
templ ActiveToolStatus(name, status, lastUsed string) {
	<div class="tool-status-item">
		<div class="tool-status-icon">
			<span class="material-symbols-rounded">build</span>
		</div>
		<div class="tool-status-content">
			<h4 class="tool-status-name">{ name }</h4>
			<div class="tool-status-info">
				<span class="status-badge active">{ status }</span>
				<span class="last-used">{ lastUsed }</span>
			</div>
		</div>
		<div class="tool-status-actions">
			<button class="action-btn-small">
				<span class="material-symbols-rounded">settings</span>
			</button>
		</div>
	</div>
}